/**
 * NewsContent - Main content area component
 * Handles different content states: empty, no results, and news display
 */

"use client";

import { NewsCard } from "./news-card";
import { NewsListItem } from "./news-list-item";
import { SwipeableNewsListItem } from "./swipeable-news-list-item";
import { But<PERSON> } from "@/components/ui/button";
import { Newspaper, RefreshCw } from "lucide-react";
import { NewsItem } from "@/types/news";
import { useNewsActions } from "@/hooks/useNewsActions";

/**
 * Props interface for NewsContent component
 */
interface NewsContentProps {
  hasNews: boolean;
  hasFilteredResults: boolean;
  hasActiveFilters: boolean;
  filteredNews: [string, NewsItem][];
  viewMode: "grid" | "list";
  onDeleteNews: (key: string) => void;
  onRefresh: () => void;
  onClearFilters: () => void;
}

/**
 * Empty state component when no news exists
 */
function EmptyNewsState({ onRefresh }: { onRefresh: () => void }) {
  return (
    <div className="flex flex-col items-center justify-center min-h-[300px] sm:min-h-[400px] space-y-4 px-4">
      <div className="text-center max-w-md">
        <div className="p-3 sm:p-4 bg-gray-100 rounded-full mb-4 inline-block">
          <Newspaper className="h-12 w-12 sm:h-16 sm:w-16 text-gray-400" />
        </div>
        <h2 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">
          No news found
        </h2>
        <p className="text-sm sm:text-base text-gray-600 mb-4 leading-relaxed">
          There are no news items available at the moment. Try refreshing the
          page or contact support if this issue persists.
        </p>
        <Button
          onClick={onRefresh}
          variant="outline"
          className="shadow-xs hover:shadow-md transition-shadow w-full sm:w-auto"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Try again
        </Button>
      </div>
    </div>
  );
}

/**
 * No results state component when filters return no matches
 */
function NoResultsState({ onClearFilters }: { onClearFilters: () => void }) {
  return (
    <div className="flex flex-col items-center justify-center min-h-[300px] sm:min-h-[400px] space-y-4 px-4">
      <div className="text-center max-w-md">
        <div className="p-3 sm:p-4 bg-gray-100 rounded-full mb-4 inline-block">
          <Newspaper className="h-12 w-12 sm:h-16 sm:w-16 text-gray-400" />
        </div>
        <h2 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">
          No results found
        </h2>
        <p className="text-sm sm:text-base text-gray-600 mb-4 leading-relaxed">
          No news items match the applied filters. Try adjusting your search
          criteria or clear the filters to see all news.
        </p>
        <Button
          onClick={onClearFilters}
          variant="outline"
          className="shadow-xs hover:shadow-md transition-shadow w-full sm:w-auto"
        >
          Clear filters
        </Button>
      </div>
    </div>
  );
}

/**
 * Grid view component for displaying news cards
 */
function GridView({
  filteredNews,
  onDeleteNews,
}: {
  filteredNews: [string, NewsItem][];
  onDeleteNews: (key: string) => void;
}) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
      {filteredNews.map(([key, newsItem]) => (
        <NewsCard
          key={key}
          newsItem={newsItem}
          newsKey={key}
          onDelete={onDeleteNews}
        />
      ))}
    </div>
  );
}

/**
 * List view component for displaying news items with swipe functionality
 */
function ListView({
  filteredNews,
  onDeleteNews,
}: {
  filteredNews: [string, NewsItem][];
  onDeleteNews: (key: string) => void;
}) {
  return (
    <div className="space-y-3 sm:space-y-4">
      {filteredNews.map(([key, newsItem]) => {
        // Create a component that handles news actions for each item
        const SwipeableItemWithActions = () => {
          const { isDeleting, isSending, deleteNews, sendNews } =
            useNewsActions({
              onDeleteSuccess: (deletedKey) => onDeleteNews(deletedKey),
              onSendSuccess: () => {
                // Success handling is done by the hook
              },
            });

          const handleDelete = async () => {
            await deleteNews(key);
          };

          const handleSend = async () => {
            if (!newsItem.newsUrl) {
              return;
            }
            await sendNews(newsItem.newsUrl);
          };

          return (
            <SwipeableNewsListItem
              newsItem={newsItem}
              newsKey={key}
              onDelete={handleDelete}
              onSend={handleSend}
              isDeleting={isDeleting}
              isSending={isSending}
            />
          );
        };

        return <SwipeableItemWithActions key={key} />;
      })}
    </div>
  );
}

/**
 * Main content component that handles different display states
 */
export function NewsContent({
  hasNews,
  hasFilteredResults,
  hasActiveFilters,
  filteredNews,
  viewMode,
  onDeleteNews,
  onRefresh,
  onClearFilters,
}: NewsContentProps) {
  // No news at all
  if (!hasNews) {
    return <EmptyNewsState onRefresh={onRefresh} />;
  }

  // Has news but no filtered results
  if (!hasFilteredResults) {
    return <NoResultsState onClearFilters={onClearFilters} />;
  }

  // Has filtered results - show the news
  return (
    <div className="mt-4 sm:mt-6">
      {viewMode === "grid" ? (
        <GridView filteredNews={filteredNews} onDeleteNews={onDeleteNews} />
      ) : (
        <ListView filteredNews={filteredNews} onDeleteNews={onDeleteNews} />
      )}
    </div>
  );
}

/**
 * Type exports for use in other modules
 */
export type { NewsContentProps };
