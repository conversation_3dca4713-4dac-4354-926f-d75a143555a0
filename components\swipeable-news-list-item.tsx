"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Send, Trash2, <PERSON><PERSON><PERSON>, Book<PERSON><PERSON>Check } from "lucide-react";
import { NewsItem } from "@/types/news";
import { NewsListItem } from "./news-list-item";
import {
  SwipeableList,
  SwipeableListItem,
  SwipeAction,
  TrailingActions,
  LeadingActions,
  Type as ListType,
} from "react-swipeable-list";
import "react-swipeable-list/dist/styles.css";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useCallback, useMemo, useState } from "react";
import { useMarkAsRead } from "@/hooks/useMarkAsRead";

interface SwipeableNewsListItemProps {
  newsItem: NewsItem;
  newsKey: string;
  onDelete: (key: string) => void;
  onSend: () => void;
  onMarkAsReadToggle?: (key: string, currentSelected: boolean) => void;
  isDeleting: boolean;
  isSending: boolean;
}

export function SwipeableNewsListItem({
  newsItem,
  newsKey,
  onDelete,
  onSend,
  onMarkAsReadToggle,
  isDeleting,
  isSending,
}: SwipeableNewsListItemProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Mark as read hook
  const { isUpdating: isMarkingAsRead, toggleMarkAsRead } = useMarkAsRead({
    onSuccess: (key, isRead) => {
      onMarkAsReadToggle?.(key, !isRead); // Call parent callback if provided
    },
    onError: (error) => {
      console.error("Mark as read operation failed:", error);
    },
  });

  const handleDeleteClick = useCallback(() => {
    setShowDeleteDialog(true);
  }, []);

  const handleConfirmDelete = useCallback(() => {
    onDelete(newsKey);
    setShowDeleteDialog(false);
  }, [onDelete, newsKey]);

  const handleMarkAsRead = useCallback(async () => {
    try {
      await toggleMarkAsRead(newsKey, newsItem.selected);
    } catch (error) {
      console.error("Error toggling mark as read:", error);
    }
  }, [toggleMarkAsRead, newsKey, newsItem.selected]);

  const leadingActions = useMemo(
    () => (
      <LeadingActions>
        {/* Mark as Read Action */}
        <SwipeAction onClick={handleMarkAsRead}>
          <div
            className={`min-w-[70px] sm:min-w-[80px] transition-colors duration-200 swipe-action flex flex-col items-center justify-center h-full px-2 sm:px-3 text-white ${
              newsItem.selected
                ? "bg-green-600 hover:bg-green-700"
                : "bg-gray-600 hover:bg-gray-700"
            }`}
          >
            {isMarkingAsRead ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 sm:h-4 sm:w-4 border-b-2 border-white mb-1" />
                <span className="text-xs font-medium hidden sm:inline">
                  Atualizando...
                </span>
                <span className="text-xs font-medium sm:hidden">...</span>
              </>
            ) : (
              <>
                {newsItem.selected ? (
                  <BookOpenCheck className="h-3 w-3 sm:h-4 sm:w-4 mb-1" />
                ) : (
                  <BookOpen className="h-3 w-3 sm:h-4 sm:w-4 mb-1" />
                )}
                <span className="text-xs font-medium">
                  {newsItem.selected ? "LIDO" : "MARCAR"}
                </span>
              </>
            )}
          </div>
        </SwipeAction>
      </LeadingActions>
    ),
    [handleMarkAsRead, isMarkingAsRead, newsItem.selected]
  );

  const trailingActions = useMemo(
    () => (
      <TrailingActions>
        {/* Send Action */}
        <SwipeAction onClick={onSend}>
          <div className="bg-blue-600 hover:bg-blue-700 text-white min-w-[70px] sm:min-w-[80px] transition-colors duration-200 swipe-action flex flex-col items-center justify-center h-full px-2 sm:px-3">
            {isSending ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 sm:h-4 sm:w-4 border-b-2 border-white mb-1" />
                <span className="text-xs font-medium hidden sm:inline">
                  Enviando...
                </span>
                <span className="text-xs font-medium sm:hidden">...</span>
              </>
            ) : (
              <>
                <Send className="h-3 w-3 sm:h-4 sm:w-4 mb-1" />
                <span className="text-xs font-medium">ENVIAR</span>
              </>
            )}
          </div>
        </SwipeAction>

        {/* Delete Action */}
        <SwipeAction onClick={handleDeleteClick} destructive={true}>
          <div className="bg-red-600 hover:bg-red-700 text-white min-w-[70px] sm:min-w-[80px] transition-colors duration-200 swipe-action flex flex-col items-center justify-center h-full px-2 sm:px-3">
            {isDeleting ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 sm:h-4 sm:w-4 border-b-2 border-white mb-1" />
                <span className="text-xs font-medium hidden sm:inline">
                  Excluindo...
                </span>
                <span className="text-xs font-medium sm:hidden">...</span>
              </>
            ) : (
              <>
                <Trash2 className="h-3 w-3 sm:h-4 sm:w-4 mb-1" />
                <span className="text-xs font-medium">EXCLUIR</span>
              </>
            )}
          </div>
        </SwipeAction>
      </TrailingActions>
    ),
    [handleDeleteClick, isDeleting, isSending, onSend]
  );

  return (
    <>
      <SwipeableList
        type={ListType.IOS}
        fullSwipe={false}
        threshold={0.25}
        swipeStartThreshold={15}
        scrollStartThreshold={10}
      >
        <SwipeableListItem
          leadingActions={leadingActions}
          trailingActions={trailingActions}
          threshold={0.25}
          className="swipeable-list-item"
        >
          <NewsListItem
            newsItem={newsItem}
            newsKey={newsKey}
            onDelete={onDelete}
            hideActions={true}
          />
        </SwipeableListItem>
      </SwipeableList>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent className="mx-4 max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-base sm:text-lg font-semibold text-gray-900">
              Confirmar exclusão
            </AlertDialogTitle>
            <AlertDialogDescription className="text-sm text-gray-600 mt-2">
              Esta ação não pode ser desfeita. A notícia será permanentemente
              removida do sistema.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex flex-col-reverse sm:flex-row gap-2 sm:gap-3">
            <AlertDialogCancel className="w-full sm:w-auto text-sm">
              Cancelar
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              disabled={isDeleting}
              className="w-full sm:w-auto bg-red-600 hover:bg-red-700 focus:ring-2 focus:ring-red-500 text-sm"
            >
              {isDeleting ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2" />
                  Excluindo...
                </>
              ) : (
                "Confirmar exclusão"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
