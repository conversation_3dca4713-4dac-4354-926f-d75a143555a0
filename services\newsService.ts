/**
 * News service layer
 * Centralizes all news-related API operations with proper error handling
 */

import { apiClient, ApiError } from './apiClient';
import { config } from '@/lib/config';
import { NewsData, NewsItem, SyncRequest, ExcludedSourcesRequest, ApiResponse } from '@/types/news';

/**
 * Service class for news-related operations
 */
export class NewsService {
  private readonly baseUrl: string;

  constructor() {
    this.baseUrl = config.firebase.databaseUrl;
  }

  /**
   * Fetches all news from the Firebase database
   * @returns Promise resolving to NewsData object
   * @throws ApiError if the request fails
   */
  async fetchAllNews(): Promise<NewsData> {
    try {
      const url = `${this.baseUrl}/news.json`;
      const data = await apiClient.get<NewsData | null>(url);

      // Firebase returns null when no data exists
      return data || {};
    } catch (error) {
      if (error instanceof ApiError) {
        throw new ApiError(
          `Failed to fetch news: ${error.message}`,
          error.status,
          error.statusText,
          error.url
        );
      }
      throw new Error(`Unexpected error while fetching news: ${error}`);
    }
  }

  /**
   * Deletes a specific news item by key
   * @param key The Firebase key of the news item to delete
   * @returns Promise that resolves when deletion is complete
   * @throws ApiError if the request fails
   */
  async deleteNews(key: string): Promise<void> {
    if (!key || typeof key !== 'string') {
      throw new Error('Invalid news key provided');
    }

    try {
      const url = `${this.baseUrl}/news/${key}.json`;
      await apiClient.delete(url);
    } catch (error) {
      if (error instanceof ApiError) {
        throw new ApiError(
          `Failed to delete news item: ${error.message}`,
          error.status,
          error.statusText,
          error.url
        );
      }
      throw new Error(`Unexpected error while deleting news: ${error}`);
    }
  }

  /**
   * Updates a specific news item by key
   * @param key The Firebase key of the news item to update
   * @param updates Partial news item data to update
   * @returns Promise that resolves when update is complete
   * @throws ApiError if the request fails
   */
  async updateNewsItem(key: string, updates: Partial<NewsItem>): Promise<void> {
    if (!key || typeof key !== 'string') {
      throw new Error('Invalid news key provided');
    }

    if (!updates || typeof updates !== 'object') {
      throw new Error('Invalid updates object provided');
    }

    try {
      const url = `${this.baseUrl}/news/${key}.json`;
      await apiClient.patch(url, updates);
    } catch (error) {
      if (error instanceof ApiError) {
        throw new ApiError(
          `Failed to update news item: ${error.message}`,
          error.status,
          error.statusText,
          error.url
        );
      }
      throw new Error(`Unexpected error while updating news: ${error}`);
    }
  }

  /**
   * Resets all news data (deletes everything)
   * @returns Promise that resolves when reset is complete
   * @throws ApiError if the request fails
   */
  async resetAllNews(): Promise<void> {
    try {
      const url = `${this.baseUrl}/news.json`;
      await apiClient.delete(url);
    } catch (error) {
      if (error instanceof ApiError) {
        throw new ApiError(
          `Failed to reset news data: ${error.message}`,
          error.status,
          error.statusText,
          error.url
        );
      }
      throw new Error(`Unexpected error while resetting news: ${error}`);
    }
  }

  /**
   * Sends news URL to external API for processing
   * @param newsUrl The URL of the news article to send
   * @returns Promise that resolves when sending is complete
   * @throws ApiError if the request fails or API URL is not configured
   */
  async sendNewsToAPI(newsUrl: string): Promise<void> {
    if (!newsUrl || typeof newsUrl !== 'string') {
      throw new Error('Invalid news URL provided');
    }

    if (!config.api.cnewsUrl) {
      throw new Error('CNEWS API URL not configured. Please set NEXT_PUBLIC_CNEWS_API_URL environment variable.');
    }

    try {
      const url = `${config.api.cnewsUrl}/process-sync`;

      // Use extended timeout and no retries for process-sync endpoint
      // as it's a synchronous processing operation that can take time
      await apiClient.post(url, { url: newsUrl }, {
        timeout: 60000, // 60 seconds timeout
        retries: 0      // No retries to prevent duplicate processing
      });
    } catch (error) {
      if (error instanceof ApiError) {
        throw new ApiError(
          `Failed to send news to API: ${error.message}`,
          error.status,
          error.statusText,
          error.url
        );
      }
      throw new Error(`Unexpected error while sending news to API: ${error}`);
    }
  }

  /**
   * Sends sync request to external API (search terms)
   * @param request The sync request data with search terms
   * @returns Promise that resolves to the API response
   * @throws ApiError if the request fails or API URL is not configured
   */
  async sync(request: SyncRequest): Promise<ApiResponse> {
    if (!request || typeof request !== 'object') {
      throw new Error('Invalid sync request provided');
    }

    if (!config.api.cnewsUrl) {
      throw new Error('CNEWS API URL not configured. Please set NEXT_PUBLIC_CNEWS_API_URL environment variable.');
    }

    try {
      const url = `${config.api.cnewsUrl}/sync`;
      const response = await apiClient.post<ApiResponse>(url, request);
      return response;
    } catch (error) {
      if (error instanceof ApiError) {
        throw new ApiError(
          `Failed to sync: ${error.message}`,
          error.status,
          error.statusText,
          error.url
        );
      }
      throw new Error(`Unexpected error while syncing: ${error}`);
    }
  }

  /**
   * Manages excluded sources via external API
   * @param request The excluded sources request data
   * @returns Promise that resolves to the API response
   * @throws ApiError if the request fails or API URL is not configured
   */
  async excludedSources(request: ExcludedSourcesRequest): Promise<ApiResponse> {
    if (!request || typeof request !== 'object') {
      throw new Error('Invalid excluded sources request provided');
    }

    if (!config.api.cnewsUrl) {
      throw new Error('CNEWS API URL not configured. Please set NEXT_PUBLIC_CNEWS_API_URL environment variable.');
    }

    try {
      const url = `${config.api.cnewsUrl}/excluded-sources`;
      const response = await apiClient.post<ApiResponse>(url, request);
      return response;
    } catch (error) {
      if (error instanceof ApiError) {
        throw new ApiError(
          `Failed to manage excluded sources: ${error.message}`,
          error.status,
          error.statusText,
          error.url
        );
      }
      throw new Error(`Unexpected error while managing excluded sources: ${error}`);
    }
  }

  /**
   * Validates a news item object
   * @param item The news item to validate
   * @returns true if valid, throws error if invalid
   */
  private validateNewsItem(item: any): item is NewsItem {
    if (!item || typeof item !== 'object') {
      throw new Error('News item must be an object');
    }

    const requiredFields = ['newsAuthor', 'newsContent', 'newsDate', 'newsTitle', 'newsUrl'];
    const missingFields = requiredFields.filter(field => !item[field]);

    if (missingFields.length > 0) {
      throw new Error(`News item missing required fields: ${missingFields.join(', ')}`);
    }

    return true;
  }

  /**
   * Validates news data structure
   * @param data The news data to validate
   * @returns true if valid, throws error if invalid
   */
  validateNewsData(data: any): data is NewsData {
    if (!data || typeof data !== 'object') {
      return true; // Empty data is valid
    }

    // Validate each news item
    for (const [key, item] of Object.entries(data)) {
      if (typeof key !== 'string') {
        throw new Error('News data keys must be strings');
      }
      this.validateNewsItem(item);
    }

    return true;
  }
}

/**
 * Default news service instance
 */
export const newsService = new NewsService();


