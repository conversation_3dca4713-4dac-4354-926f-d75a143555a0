/* Custom styles for react-swipeable-list integration */

/* Override default swipeable list styles to match our design */
.swipeable-list-item {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Ensure the swipe actions have proper styling */
.swipe-action {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100% !important;
  min-height: 80px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease-in-out !important;
  border: none !important;
  outline: none !important;
}

/* Responsive adjustments for swipe actions */
@media (max-width: 640px) {
  .swipe-action {
    min-height: 70px !important;
    min-width: 70px !important;
  }

  .swipe-action span {
    font-size: 0.75rem !important;
  }
}

@media (min-width: 641px) {
  .swipe-action {
    min-height: 80px !important;
    min-width: 80px !important;
  }
}

/* Ensure proper touch targets for mobile */
.swipe-action {
  touch-action: manipulation !important;
  -webkit-tap-highlight-color: transparent !important;
  cursor: pointer !important;
}

/* Smooth transitions for swipe animations */
.swipeable-list-item [class*="swipeable-list"] {
  transition: transform 0.2s ease-out !important;
}

/* Ensure the news item content doesn't interfere with swipe gestures */
.swipeable-list-item .group {
  position: relative !important;
  z-index: 1 !important;
}

/* Override any conflicting styles from the library */
.swipeable-list-item [class*="swipeable-list-item"] {
  background: transparent !important;
  border-radius: 0 !important;
}

/* Ensure proper spacing and layout */
.swipeable-list-item {
  margin-bottom: 0.75rem !important;
}

@media (min-width: 640px) {
  .swipeable-list-item {
    margin-bottom: 1rem !important;
  }
}