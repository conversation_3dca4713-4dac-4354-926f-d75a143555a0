/**
 * Custom hook for news actions (delete, send)
 * Centralizes action logic with proper loading states and error handling
 */

import { useState, useCallback, useRef } from 'react';
import { newsService } from '@/services/newsService';
import { ApiError } from '@/services/apiClient';
import { toast } from 'sonner';

/**
 * Hook return type definition
 */
interface UseNewsActionsReturn {
  // Loading states
  isDeleting: boolean;
  isSending: boolean;

  // Actions
  deleteNews: (key: string) => Promise<void>;
  sendNews: (newsUrl: string, newsKey?: string) => Promise<void>;

  // Utility
  isPerformingAction: boolean;
}

/**
 * Options for the useNewsActions hook
 */
interface UseNewsActionsOptions {
  onDeleteSuccess?: (key: string) => void;
  onDeleteError?: (error: string, key: string) => void;
  onSendSuccess?: (url: string) => void;
  onSendError?: (error: string, url: string) => void;
}

/**
 * Custom hook for news actions
 * @param options Configuration options for the hook
 * @returns Object containing action functions and loading states
 */
export function useNewsActions(options: UseNewsActionsOptions = {}): UseNewsActionsReturn {
  const { onDeleteSuccess, onDeleteError, onSendSuccess, onSendError } = options;

  // State management
  const [isDeleting, setIsDeleting] = useState(false);
  const [isSending, setIsSending] = useState(false);

  // Ref to track if component is mounted (prevents state updates after unmount)
  const isMountedRef = useRef(true);

  /**
   * Safely updates state only if component is still mounted
   */
  const safeSetState = useCallback(<T>(setter: (value: T) => void, value: T) => {
    if (isMountedRef.current) {
      setter(value);
    }
  }, []);

  /**
   * Handles errors consistently across all actions
   */
  const handleError = useCallback((
    error: unknown,
    operation: string,
    onError?: (error: string, context: string) => void,
    context?: string
  ) => {
    let errorMessage: string;

    if (error instanceof ApiError) {
      errorMessage = error.message;
      if (error.status) {
        errorMessage += ` (HTTP ${error.status})`;
      }
    } else if (error instanceof Error) {
      errorMessage = error.message;
    } else {
      errorMessage = 'Unknown error occurred';
    }

    console.error(`News action error [${operation}]:`, error);

    onError?.(errorMessage, context || '');

    toast.error(`Error: ${operation}`, {
      description: errorMessage,
    });
  }, []);

  /**
   * Deletes a news item
   * @param key The Firebase key of the news item to delete
   */
  const deleteNews = useCallback(async (key: string) => {
    if (!key || typeof key !== 'string') {
      const errorMsg = 'Invalid news key provided';
      handleError(new Error(errorMsg), 'Delete News', onDeleteError, key);
      return;
    }

    safeSetState(setIsDeleting, true);

    try {
      await newsService.deleteNews(key);

      onDeleteSuccess?.(key);

      toast.success('News item removed successfully!', {
        description: 'The news item has been permanently deleted from the system.',
      });
    } catch (error) {
      handleError(error, 'Delete News', onDeleteError, key);
    } finally {
      safeSetState(setIsDeleting, false);
    }
  }, [safeSetState, handleError, onDeleteSuccess, onDeleteError]);

  /**
   * Sends a news URL to the external API and marks it as read
   * @param newsUrl The URL of the news article to send
   * @param newsKey The Firebase key of the news item (optional, for auto-marking as read)
   */
  const sendNews = useCallback(async (newsUrl: string, newsKey?: string) => {
    if (!newsUrl || typeof newsUrl !== 'string') {
      const errorMsg = 'Invalid news URL provided';
      handleError(new Error(errorMsg), 'Send News', onSendError, newsUrl);
      return;
    }

    // Validate URL format
    try {
      new URL(newsUrl);
    } catch {
      const errorMsg = 'Invalid URL format provided';
      handleError(new Error(errorMsg), 'Send News', onSendError, newsUrl);
      return;
    }

    // Prevent multiple simultaneous sends
    if (isSending) {
      toast.warning('Send operation already in progress', {
        description: 'Please wait for the current operation to complete.',
      });
      return;
    }

    safeSetState(setIsSending, true);

    try {
      await newsService.sendNewsToAPI(newsUrl);

      // Auto-mark as read if newsKey is provided
      if (newsKey) {
        try {
          await newsService.updateNewsItem(newsKey, { selected: true });
        } catch (markAsReadError) {
          console.warn('Failed to auto-mark news as read after sending:', markAsReadError);
          // Don't fail the entire operation if marking as read fails
        }
      }

      onSendSuccess?.(newsUrl);

      toast.success('News sent successfully!', {
        description: 'The news article has been sent to the processing API and marked as read.',
      });
    } catch (error) {
      handleError(error, 'Send News', onSendError, newsUrl);
    } finally {
      safeSetState(setIsSending, false);
    }
  }, [isSending, safeSetState, handleError, onSendSuccess, onSendError]);

  /**
   * Computed property indicating if any action is in progress
   */
  const isPerformingAction = isDeleting || isSending;

  // Cleanup function to prevent state updates after unmount
  const cleanup = useCallback(() => {
    isMountedRef.current = false;
  }, []);

  // Return the hook interface
  return {
    // Loading states
    isDeleting,
    isSending,

    // Actions
    deleteNews,
    sendNews,

    // Utility
    isPerformingAction,
  };
}

/**
 * Type exports for use in other modules
 */
export type { UseNewsActionsReturn, UseNewsActionsOptions };
