/**
 * Custom hook for managing mark as read functionality
 * Provides optimized mark as read operations with proper loading states and error handling
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { newsService } from '@/services/newsService';
import { ApiError } from '@/services/apiClient';
import { toast } from 'sonner';

/**
 * Hook options interface
 */
interface UseMarkAsReadOptions {
  onSuccess?: (key: string, isRead: boolean) => void;
  onError?: (error: Error, key: string) => void;
}

/**
 * Hook return type definition
 */
interface UseMarkAsReadReturn {
  // Loading states
  isUpdating: boolean;

  // Actions
  toggleMarkAsRead: (key: string, currentSelected: boolean) => Promise<void>;
  markAsRead: (key: string) => Promise<void>;
  markAsUnread: (key: string) => Promise<void>;

  // Utility
  isPerformingAction: boolean;
}

/**
 * Custom hook for mark as read functionality
 * @param options Configuration options for the hook
 * @returns Object containing action functions and loading states
 */
export function useMarkAsRead(options: UseMarkAsReadOptions = {}): UseMarkAsReadReturn {
  const { onSuccess, onError } = options;

  // State management
  const [isUpdating, setIsUpdating] = useState(false);

  // Ref to track if component is mounted (prevents state updates after unmount)
  const isMountedRef = useRef(true);

  /**
   * Safely updates state only if component is still mounted
   */
  const safeSetState = useCallback(<T>(setter: (value: T) => void, value: T) => {
    if (isMountedRef.current) {
      setter(value);
    }
  }, []);

  /**
   * Centralized error handling
   */
  const handleError = useCallback((error: unknown, operation: string, key: string) => {
    console.error(`${operation} failed for key ${key}:`, error);

    let errorMessage = 'An unexpected error occurred';

    if (error instanceof ApiError) {
      errorMessage = error.message;
    } else if (error instanceof Error) {
      errorMessage = error.message;
    }

    toast.error(`${operation} failed`, {
      description: errorMessage,
    });

    onError?.(error instanceof Error ? error : new Error(String(error)), key);
  }, [onError]);

  /**
   * Updates the selected status of a news item
   * @param key The Firebase key of the news item
   * @param selected The new selected status
   */
  const updateSelectedStatus = useCallback(async (key: string, selected: boolean) => {
    if (!key || typeof key !== 'string') {
      const errorMsg = 'Invalid news key provided';
      handleError(new Error(errorMsg), 'Mark as Read', key);
      return;
    }

    // Prevent multiple simultaneous updates
    if (isUpdating) {
      toast.warning('Update operation already in progress', {
        description: 'Please wait for the current operation to complete.',
      });
      return;
    }

    safeSetState(setIsUpdating, true);

    try {
      await newsService.updateNewsItem(key, { selected });

      onSuccess?.(key, selected);

      const actionText = selected ? 'marked as read' : 'marked as unread';
      toast.success(`News ${actionText}!`, {
        description: 'The news item status has been updated.',
      });
    } catch (error) {
      const operation = selected ? 'Mark as Read' : 'Mark as Unread';
      handleError(error, operation, key);
    } finally {
      safeSetState(setIsUpdating, false);
    }
  }, [isUpdating, safeSetState, handleError, onSuccess]);

  /**
   * Toggles the mark as read status of a news item
   * @param key The Firebase key of the news item
   * @param currentSelected The current selected status
   */
  const toggleMarkAsRead = useCallback(async (key: string, currentSelected: boolean) => {
    await updateSelectedStatus(key, !currentSelected);
  }, [updateSelectedStatus]);

  /**
   * Marks a news item as read
   * @param key The Firebase key of the news item
   */
  const markAsRead = useCallback(async (key: string) => {
    await updateSelectedStatus(key, true);
  }, [updateSelectedStatus]);

  /**
   * Marks a news item as unread
   * @param key The Firebase key of the news item
   */
  const markAsUnread = useCallback(async (key: string) => {
    await updateSelectedStatus(key, false);
  }, [updateSelectedStatus]);

  /**
   * Computed property indicating if any action is in progress
   */
  const isPerformingAction = isUpdating;

  // Cleanup function to prevent state updates after unmount
  const cleanup = useCallback(() => {
    isMountedRef.current = false;
  }, []);

  // Effect to handle cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    // Loading states
    isUpdating,

    // Actions
    toggleMarkAsRead,
    markAsRead,
    markAsUnread,

    // Utility
    isPerformingAction,
  };
}

/**
 * Type exports for use in other modules
 */
export type { UseMarkAsReadReturn, UseMarkAsReadOptions };
