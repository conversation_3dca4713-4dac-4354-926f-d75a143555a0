"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Trash2,
  Send,
  MoreVertical,
  ExternalLink,
  Copy,
  CheckCircle,
  UserX,
  BookOpen,
  BookOpenCheck,
} from "lucide-react";
import { toast } from "sonner";
import { useExcludedSources } from "@/hooks/useExcludedSources";
import { useMarkAsRead } from "@/hooks/useMarkAsRead";

interface NewsActionsProps {
  newsKey: string;
  newsUrl: string;
  newsAuthor: string;
  selected: boolean;
  onDelete: (key: string) => void;
  onSend: () => void;
  onMarkAsReadToggle?: (key: string, currentSelected: boolean) => void;
  isDeleting: boolean;
  isSending: boolean;
}

export function NewsActions({
  newsKey,
  newsUrl,
  newsAuthor,
  selected,
  onDelete,
  onSend,
  onMarkAsReadToggle,
  isDeleting,
  isSending,
}: NewsActionsProps) {
  const [copied, setCopied] = useState(false);

  // Excluded sources hook
  const { isProcessing: isExcludingSource, addExcludedSources } =
    useExcludedSources({
      onSuccess: (response, action, sources) => {
        console.log(`Successfully ${action}ed sources:`, sources);
      },
      onError: (error) => {
        console.error("Excluded sources operation failed:", error);
      },
    });

  // Mark as read hook
  const { isUpdating: isMarkingAsRead, toggleMarkAsRead } = useMarkAsRead({
    onSuccess: (key, isRead) => {
      onMarkAsReadToggle?.(key, !isRead); // Call parent callback if provided
    },
    onError: (error) => {
      console.error("Mark as read operation failed:", error);
    },
  });

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(newsUrl);
      setCopied(true);
      toast.success("URL copiada para a área de transferência");
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error("Erro ao copiar URL");
    }
  };

  const handleExcludeSource = async () => {
    if (!newsAuthor) {
      toast.error("Autor da notícia não encontrado");
      return;
    }

    try {
      await addExcludedSources([newsAuthor]);
    } catch (error) {
      console.error("Error excluding source:", error);
    }
  };

  const handleMarkAsRead = async () => {
    try {
      await toggleMarkAsRead(newsKey, selected);
    } catch (error) {
      console.error("Error toggling mark as read:", error);
    }
  };

  return (
    <div className="flex items-center gap-1 sm:gap-2 flex-wrap sm:flex-nowrap">
      {/* Botão de Enviar - Sempre visível */}
      <Button
        size="lg"
        onClick={onSend}
        disabled={isSending}
        className="bg-blue-600 hover:bg-blue-700 text-white shadow-sm transition-all duration-200 hover:shadow-md focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 min-w-[70px] sm:min-w-[85px] h-8 sm:h-9 text-xs sm:text-sm"
      >
        {isSending ? (
          <>
            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1 flex-shrink-0" />
            <span className="hidden sm:inline">Enviando...</span>
            <span className="sm:hidden">...</span>
          </>
        ) : (
          <>
            <Send className="h-3 w-3 mr-1 flex-shrink-0" />
            <span>Enviar</span>
          </>
        )}
      </Button>

      {/* Botão de Marcar como Lido/Não Lido */}
      <Button
        variant="outline"
        size="lg"
        onClick={handleMarkAsRead}
        disabled={isMarkingAsRead}
        className={`transition-all duration-200 focus:ring-2 focus:ring-offset-2 min-w-[70px] sm:min-w-[85px] h-8 sm:h-9 text-xs sm:text-sm ${
          selected
            ? "border-green-200 text-green-700 hover:bg-green-50 hover:border-green-300 hover:text-green-800 focus:ring-green-500"
            : "border-gray-200 text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 focus:ring-gray-500"
        }`}
      >
        {isMarkingAsRead ? (
          <>
            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current mr-1 flex-shrink-0" />
            <span className="hidden sm:inline">Atualizando...</span>
            <span className="sm:hidden">...</span>
          </>
        ) : (
          <>
            {selected ? (
              <BookOpenCheck className="h-3 w-3 mr-1 flex-shrink-0" />
            ) : (
              <BookOpen className="h-3 w-3 mr-1 flex-shrink-0" />
            )}
            <span className="hidden sm:inline">
              {selected ? "Lido" : "Marcar"}
            </span>
            <span className="sm:hidden">{selected ? "✓" : "○"}</span>
          </>
        )}
      </Button>

      {/* Botão de Excluir - Agora visível */}
      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button
            variant="outline"
            size="lg"
            disabled={isDeleting}
            className="border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 hover:text-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 min-w-[70px] sm:min-w-[85px] h-8 sm:h-9 text-xs sm:text-sm"
          >
            {isDeleting ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-red-600 mr-1 flex-shrink-0" />
                <span className="hidden sm:inline">Excluindo...</span>
                <span className="sm:hidden">...</span>
              </>
            ) : (
              <>
                <Trash2 className="h-3 w-3 mr-1 flex-shrink-0" />
                <span>Excluir</span>
              </>
            )}
          </Button>
        </AlertDialogTrigger>

        <AlertDialogContent className="mx-4 max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-base sm:text-lg font-semibold text-gray-900">
              Confirmar exclusão
            </AlertDialogTitle>
            <AlertDialogDescription className="text-sm text-gray-600 mt-2">
              Esta ação não pode ser desfeita. A notícia será permanentemente
              removida do sistema.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex flex-col-reverse sm:flex-row gap-2 sm:gap-3">
            <AlertDialogCancel className="w-full sm:w-auto text-sm">
              Cancelar
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => onDelete(newsKey)}
              disabled={isDeleting}
              className="w-full sm:w-auto bg-red-600 hover:bg-red-700 focus:ring-2 focus:ring-red-500 text-sm"
            >
              {isDeleting ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2 flex-shrink-0" />
                  Excluindo...
                </>
              ) : (
                "Confirmar exclusão"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Menu de Ações Secundárias */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="h-8 w-8 p-0 hover:bg-gray-50 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 flex-shrink-0"
            aria-label="Mais opções"
          >
            <MoreVertical className="h-3 w-3" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className="w-44 sm:w-48 shadow-lg border border-gray-200"
          sideOffset={4}
        >
          <DropdownMenuItem
            onClick={() => window.open(newsUrl, "_blank")}
            className="cursor-pointer hover:bg-gray-50 focus:bg-gray-50 text-sm"
          >
            <ExternalLink className="h-4 w-4 mr-2 text-gray-500 flex-shrink-0" />
            <span className="text-gray-700 truncate">Abrir notícia</span>
          </DropdownMenuItem>

          <DropdownMenuItem
            onClick={handleCopyUrl}
            className="cursor-pointer hover:bg-gray-50 focus:bg-gray-50 text-sm"
          >
            {copied ? (
              <CheckCircle className="h-4 w-4 mr-2 text-green-600 flex-shrink-0" />
            ) : (
              <Copy className="h-4 w-4 mr-2 text-gray-500 flex-shrink-0" />
            )}
            <span
              className={`truncate ${
                copied ? "text-green-700" : "text-gray-700"
              }`}
            >
              {copied ? "Copiado!" : "Copiar URL"}
            </span>
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          <DropdownMenuItem
            onClick={handleExcludeSource}
            disabled={isExcludingSource}
            className="cursor-pointer text-orange-600 hover:bg-orange-50 focus:bg-orange-50 focus:text-orange-700 text-sm"
          >
            <UserX className="h-4 w-4 mr-2 flex-shrink-0" />
            <span className="truncate">
              {isExcludingSource ? "Excluindo fonte..." : "Excluir fonte"}
            </span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
