"use client";

import Image from "next/image";
import { useState } from "react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { NewsActions } from "@/components/news-actions";
import { Clock, User, ExternalLink } from "lucide-react";
import { NewsItem } from "@/types/news";
import { useNewsActions } from "@/hooks/useNewsActions";
import { parseBrazilianDate, formatBrazilianDate } from "@/lib/utils";

interface NewsCardProps {
  newsItem: NewsItem;
  newsKey: string;
  onDelete: (key: string) => void;
}

export function NewsCard({ newsItem, newsKey, onDelete }: NewsCardProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Use the new actions hook
  const { isDeleting, isSending, deleteNews, sendNews } = useNewsActions({
    onDeleteSuccess: (key) => onDelete(key),
    onSendSuccess: () => {
      // Success handling is done by the hook
    },
  });

  const handleDelete = async () => {
    await deleteNews(newsKey);
  };

  const handleSend = async () => {
    if (!newsItem.newsUrl) {
      return;
    }
    await sendNews(newsItem.newsUrl, newsKey);
  };

  const formatDate = (dateString: string) => {
    try {
      const date = parseBrazilianDate(dateString);
      if (!date) {
        return dateString;
      }

      const now = new Date();
      const diffTime = Math.abs(now.getTime() - date.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays === 1) return "Hoje";
      if (diffDays === 2) return "Ontem";
      if (diffDays <= 7) return `${diffDays - 1} dias atrás`;

      return formatBrazilianDate(date);
    } catch {
      return dateString;
    }
  };

  return (
    <Card className="group relative overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1 bg-white border border-gray-200 h-full flex flex-col">
      {/* Header com Autor */}
      <CardHeader className="pb-2 sm:pb-3 p-3 sm:p-6 flex-shrink-0">
        <div className="flex items-start justify-between gap-2 sm:gap-3">
          <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
            <Avatar className="h-8 w-8 sm:h-10 sm:w-10 ring-2 ring-gray-100 flex-shrink-0">
              <AvatarImage
                src={newsItem.newsAuthorImg}
                alt={newsItem.newsAuthor}
                onLoad={() => setImageLoaded(true)}
              />
              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold text-xs">
                {newsItem.newsAuthor
                  .split(" ")
                  .map((n) => n[0])
                  .join("")
                  .slice(0, 2)
                  .toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="min-w-0 flex-1">
              <div className="flex items-center space-x-1 sm:space-x-2">
                <User className="h-3 w-3 text-gray-400 flex-shrink-0" />
                <p className="font-semibold text-gray-900 text-xs sm:text-sm truncate">
                  {newsItem.newsAuthor}
                </p>
              </div>
              <div className="flex items-center space-x-1 sm:space-x-2 mt-0.5 sm:mt-1">
                <Clock className="h-3 w-3 text-gray-400 flex-shrink-0" />
                <p className="text-xs text-gray-500 truncate">
                  {formatDate(newsItem.newsDate)}
                </p>
              </div>
            </div>
          </div>

          {newsItem.selected && (
            <Badge
              variant="default"
              className="bg-green-100 text-green-800 hover:bg-green-100 flex-shrink-0 text-xs"
            >
              <span className="hidden sm:inline">✓ Selecionado</span>
              <span className="sm:hidden">✓</span>
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-3 sm:space-y-4 flex-1 flex flex-col p-3 sm:p-6 pt-0">
        {/* Título */}
        {newsItem.newsTitle && (
          <h3 className="font-bold text-base sm:text-lg leading-tight text-gray-900 line-clamp-2 group-hover:text-blue-700 transition-colors">
            {newsItem.newsTitle}
          </h3>
        )}

        {/* Imagem */}
        {newsItem.newsImg && !imageError && (
          <div className="relative overflow-hidden rounded-xl sm:rounded-2xl bg-gray-100">
            <div
              className={`aspect-video transition-opacity duration-300 ${
                imageLoaded ? "opacity-100" : "opacity-0"
              }`}
            >
              <Image
                src={newsItem.newsImg}
                alt={newsItem.newsTitle || "Notícia"}
                fill
                className="object-cover transition-transform duration-500 group-hover:scale-105"
                onLoad={() => setImageLoaded(true)}
                onError={() => setImageError(true)}
                sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
              />
            </div>
            {!imageLoaded && (
              <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
                <div className="w-6 h-6 sm:w-8 sm:h-8 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin" />
              </div>
            )}
          </div>
        )}

        {/* Conteúdo */}
        <div className="space-y-2 sm:space-y-3 flex-1">
          <p className="text-gray-700 text-xs sm:text-sm leading-relaxed line-clamp-3">
            {newsItem.newsContent}
          </p>

          {newsItem.newsUrl && (
            <a
              href={newsItem.newsUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center space-x-1 text-xs text-blue-600 hover:text-blue-800 hover:underline font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded"
            >
              <ExternalLink className="h-3 w-3 flex-shrink-0" />
              <span>Ler artigo completo</span>
            </a>
          )}
        </div>

        {/* Ações - Sempre no final */}
        <div className="flex justify-center pt-3 sm:pt-4 border-t border-gray-100 mt-auto">
          <NewsActions
            newsKey={newsKey}
            newsUrl={newsItem.newsUrl}
            newsAuthor={newsItem.newsAuthor}
            selected={newsItem.selected}
            onDelete={handleDelete}
            onSend={handleSend}
            isDeleting={isDeleting}
            isSending={isSending}
          />
        </div>
      </CardContent>
    </Card>
  );
}
