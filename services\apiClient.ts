/**
 * Centralized HTTP client for API communications
 * Provides consistent error handling, timeouts, and retry logic
 */

import { config } from '@/lib/config';

/**
 * Custom error class for API-related errors
 */
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public statusText?: string,
    public url?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

/**
 * HTTP client configuration options
 */
interface RequestOptions extends RequestInit {
  timeout?: number;
  retries?: number;
}

/**
 * Implements exponential backoff for retry logic
 * @param attempt Current attempt number (0-based)
 * @returns Delay in milliseconds
 */
function getRetryDelay(attempt: number): number {
  return Math.min(1000 * Math.pow(2, attempt), 10000); // Max 10 seconds
}

/**
 * Enhanced fetch with timeout, retry logic, and error handling
 * @param url Request URL
 * @param options Request options including custom timeout and retries
 * @returns Promise resolving to Response object
 */
async function fetchWithRetry(url: string, options: RequestOptions = {}): Promise<Response> {
  const {
    timeout = config.api.timeout,
    retries = config.api.retryAttempts,
    ...fetchOptions
  } = options;

  let lastError: Error = new Error('Unknown error');

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      // Create abort controller for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        controller.abort(new Error(`Request timeout after ${timeout}ms`));
      }, timeout);

      const response = await fetch(url, {
        ...fetchOptions,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Don't retry on successful responses or client errors (4xx)
      if (response.ok || (response.status >= 400 && response.status < 500)) {
        return response;
      }

      // Server errors (5xx) should be retried
      throw new ApiError(
        `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        response.statusText,
        url
      );
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));

      // Check if it's an abort error (timeout)
      if (error instanceof Error && error.name === 'AbortError') {
        lastError = new Error(`Request timeout after ${timeout}ms`);
      }

      // Don't retry on abort (timeout) or network errors on the last attempt
      if (attempt === retries) {
        break;
      }

      // Don't retry on timeout errors when retries is 0 (like for process-sync)
      if (retries === 0 || (error instanceof Error && error.name === 'AbortError')) {
        break;
      }

      // Wait before retrying (exponential backoff)
      if (attempt < retries) {
        await new Promise(resolve => setTimeout(resolve, getRetryDelay(attempt)));
      }
    }
  }

  // If we get here, all retries failed
  throw new ApiError(
    `Request failed after ${retries + 1} attempts: ${lastError.message}`,
    undefined,
    undefined,
    url
  );
}

/**
 * HTTP client class with common request methods
 */
export class ApiClient {
  private baseHeaders: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  /**
   * Performs a GET request
   * @param url Request URL
   * @param options Additional request options
   * @returns Promise resolving to parsed JSON response
   */
  async get<T = any>(url: string, options: RequestOptions = {}): Promise<T> {
    const response = await fetchWithRetry(url, {
      method: 'GET',
      headers: this.baseHeaders,
      ...options,
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => '');
      throw new ApiError(
        `GET ${url} failed: ${response.statusText}${errorText ? ` - ${errorText}` : ''}`,
        response.status,
        response.statusText,
        url
      );
    }

    return response.json();
  }

  /**
   * Performs a POST request
   * @param url Request URL
   * @param data Request body data
   * @param options Additional request options
   * @returns Promise resolving to parsed JSON response
   */
  async post<T = any>(url: string, data?: any, options: RequestOptions = {}): Promise<T> {
    const response = await fetchWithRetry(url, {
      method: 'POST',
      headers: this.baseHeaders,
      body: data ? JSON.stringify(data) : undefined,
      ...options,
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => '');
      throw new ApiError(
        `POST ${url} failed: ${response.statusText}${errorText ? ` - ${errorText}` : ''}`,
        response.status,
        response.statusText,
        url
      );
    }

    return response.json();
  }

  /**
   * Performs a PATCH request
   * @param url Request URL
   * @param data Request body data
   * @param options Additional request options
   * @returns Promise resolving to parsed JSON response
   */
  async patch<T = any>(url: string, data?: any, options: RequestOptions = {}): Promise<T> {
    const response = await fetchWithRetry(url, {
      method: 'PATCH',
      headers: this.baseHeaders,
      body: data ? JSON.stringify(data) : undefined,
      ...options,
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => '');
      throw new ApiError(
        `PATCH ${url} failed: ${response.statusText}${errorText ? ` - ${errorText}` : ''}`,
        response.status,
        response.statusText,
        url
      );
    }

    return response.json();
  }

  /**
   * Performs a DELETE request
   * @param url Request URL
   * @param options Additional request options
   * @returns Promise resolving to Response object
   */
  async delete(url: string, options: RequestOptions = {}): Promise<Response> {
    const response = await fetchWithRetry(url, {
      method: 'DELETE',
      headers: this.baseHeaders,
      ...options,
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => '');
      throw new ApiError(
        `DELETE ${url} failed: ${response.statusText}${errorText ? ` - ${errorText}` : ''}`,
        response.status,
        response.statusText,
        url
      );
    }

    return response;
  }
}

/**
 * Default API client instance
 */
export const apiClient = new ApiClient();
